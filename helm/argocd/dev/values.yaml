image:
  repository: fips-registry.us-east-1.build.socure.link/ai/ai-bnymellon-service
    tag: "OVERRIDE_ME"

serviceAccount:
  name: "ai-bnymellon-service-dev"
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-irsa-aa67d8b-dev-c3303b2e

application:
  env:
    CONFIGURATION_NAME: "ai-bnymellon-service"
    CONFIGURATION_VERSION: "OVERRIDE_ME"
    DD_CONSTANT_TAGS: "ddname:ai-bnymellon-service"
#    JAVA_TOOL_OPTIONS: "-XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=85.0 -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:NewRatio=1 -Dorg.bouncycastle.jca.enable_jks=true -javaagent:/opt/socure/dd-java-agent.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"
#    JAVA_TOOL_OPTIONS: "-XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=85.0 -Djavax.net.ssl.trustStore=/usr/lib/jvm/jre-8-openjdk/lib/security/cacerts -Djavax.net.ssl.trustStorePassword=changeit -Djavax.net.ssl.trustStoreType=JKS -Dorg.bouncycastle.jca.enable_jks=true -javaagent:/opt/socure/dd-java-agent-1.34.0.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"
    JAVA_TOOL_OPTIONS: "-Dorg.bouncycastle.jca.enable_jks=true -Djavax.net.ssl.keyStoreType=JKS -Djavax.net.ssl.trustStoreType=JKS -javaagent:/opt/socure/dd-java-agent-1.34.0.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"

deployment:
  resources:
    limits:
      cpu: "3"
      memory: "10Gi"
    requests:
      cpu: "500m"
      memory: "4Gi"

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 3
  targetCPUUtilizationPercentage: 30
  targetMemoryUtilizationPercentage: 30

istio:
  enabled: true
  hosts:
    - ai-bnymellon-service.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  authorizationPolicy:
    enabled: true
    pa:
      enabled: true
      serviceAccounts:
        - "ai-gateway-service-dev"
