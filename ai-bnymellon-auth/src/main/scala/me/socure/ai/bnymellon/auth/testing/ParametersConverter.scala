package me.socure.ai.bnymellon.auth.testing

import org.json4s.JsonAST.JField
import org.json4s.jackson.JsonMethods

object ParametersConverter {

  private val transformKeysMap: Map[String, String] = Map (
    "firstname" -> "firstName",
    "surname" -> "surName",
    "companyname" -> "companyName",
    "physicaladdress" -> "physicalAddress",
    "physicaladdress2" -> "physicalAddress2",
    "ipaddress" -> "ipAddress",
    "mobilenumber" -> "mobileNumber",
    "nationalid" -> "nationalId",
    "driverlicense" -> "driverLicense",
    "driverlicensestate" -> "driverLicenseState",
    "impersonatorapikey" -> "impersonatorApi<PERSON>ey",
    "userid" -> "userId",
    "customeruserid" -> "customerUserId",
    "runid" -> "runId",
    "nocache" -> "noCache",
    "forcerefresh" -> "forceRefresh",
    "datascience" -> "dataScience",
    "fullname" -> "fullName",
    "orderchannel" -> "orderChannel",
    "lastorderdate" -> "lastOrderDate",
    "prevordercount" -> "prevOrderCount",
    "accountcreationdate" -> "accountCreationDate",
    "orderamount" -> "orderAmount",
    "submissiondate" -> "submissionDate",
    "documentuuid" -> "documentUuid",
    "watchlistfilters" -> "watchlistFilters"
  )

  def convert(requestBody: String): String = {
    val inputWithoutNulls = JsonMethods.parse(requestBody).noNulls
    val transformedInput = inputWithoutNulls.transformField {
      case JField(key, value) if (transformKeysMap.contains(key)) => JField(transformKeysMap.get(key).get, value)
    }.removeField(x => x._1.equalsIgnoreCase("socurekey"))
    JsonMethods.compact(JsonMethods.render(transformedInput))
  }

  def main(args: Array[String]): Unit = {
    println(convert("{\n  \"prevUnpaidOrderCount\": null,\n  \"country\": \"US\",\n  \"uniqueIdAccount\": null,\n  \"addresses\": null,\n  \"transactionLocation\": null,\n  \"notes\": null,\n  \"submissiondate\": \"2025-03-07\",\n  \"authenticationContext\": null,\n  \"counterPartyType\": null,\n  \"businessName\": null,\n  \"channel\": null,\n  \"mcc\": null,\n  \"userid\": null,\n  \"consentTimestamp\": null,\n  \"deviceSessionId\": null,\n  \"riskOSId\": null,\n  \"physicaladdress2\": \"\",\n  \"counterPartyAccount\": null,\n  \"orderamount\": null,\n  \"nocache\": 0,\n  \"details\": false,\n  \"state\": \"MO\",\n  \"runid\": null,\n  \"isConsenting\": null,\n  \"customeruserid\": \"18699\",\n  \"vendors\": null,\n  \"zip\": \"65738\",\n  \"workflow\": null,\n  \"merchant\": null,\n  \"orderchannel\": null,\n  \"datascience\": false,\n  \"stepupReason\": null,\n  \"modules\": [\n    \"accountintelligencepremier\"\n  ],\n  \"physicaladdress\": \"4813 SOUTH DOUGLAS DRIVE\",\n  \"driverlicensestate\": null,\n  \"customerAccountId\": null,\n  \"entitytype\": null,\n  \"forcerefresh\": null,\n  \"companyname\": null,\n  \"dob\": \"1936-09-26\",\n  \"socurekey\": \"a96e0893-9b9b-456a-a9a3-09c102a21fb3\",\n  \"businessPhone\": null,\n  \"device\": null,\n  \"driverlicense\": null,\n  \"ipaddress\": \"\",\n  \"firstname\": \"DARRELL\",\n  \"uniqueIdTransaction\": null,\n  \"mobilenumber\": \"\",\n  \"impersonatorapikey\": null,\n  \"previousReferenceId\": null,\n  \"gender\": null,\n  \"city\": \"REPUBLIC\",\n  \"counterPartyRouting\": null,\n  \"payments\": {\n    \"recipientCountry\": null,\n    \"paymentType\": null,\n    \"disbursementType\": null,\n    \"account\": {\n      \"accountNumber\": \"*********\",\n      \"routingNumber\": \"*********\",\n      \"accountCountry\": null,\n      \"inquiries\": [\n        \"STATUS\",\n        \"OWNERSHIP\"\n      ],\n      \"validInquiries\": true\n    },\n    \"paymentTypeDetail\": \"\",\n    \"productType\": \"\",\n    \"cardFirst6\": \"\",\n    \"cardLast4\": \"\",\n    \"currencyType\": \"\"\n  },\n  \"entityname\": null,\n  \"description\": null,\n  \"ein\": null,\n  \"acceptlist\": false,\n  \"nationalid\": \"*********\",\n  \"shippingDetails\": null,\n  \"initiationDate\": null,\n  \"docvTransactionToken\": null,\n  \"surname\": \"HANSON\",\n  \"accountcreationdate\": null,\n  \"watchlistfilters\": null,\n  \"prevordercount\": 0,\n  \"segmentation\": null,\n  \"email\": \"\",\n  \"direction\": null,\n  \"amount\": null,\n  \"debug\": false,\n  \"mnostatus\": false,\n  \"counterPartyInstitution\": null,\n  \"documentuuid\": null,\n  \"lastorderdate\": null,\n  \"userName\": null,\n  \"merchantDetails\": null,\n  \"useCase\": null,\n  \"disclosurePurpose\": null,\n  \"parentTxnId\": null,\n  \"uniqueIdUser\": null,\n  \"userConsent\": null,\n  \"geocode\": null,\n  \"fullname\": null,\n  \"countryOfOrigin\": null,\n  \"homeNumber\": null\n}"))
  }

}
